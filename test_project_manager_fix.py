#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试项目管理器修复
验证两个项目管理器的同步是否正常工作
"""

import os
import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.project_manager import ProjectManager
from src.utils.project_manager import StoryboardProjectManager
from src.utils.logger import logger

def test_project_manager_sync():
    """测试项目管理器同步"""
    try:
        logger.info("开始测试项目管理器同步...")
        
        # 初始化两个项目管理器
        new_pm = ProjectManager()
        old_pm = StoryboardProjectManager("config")
        
        logger.info(f"新项目管理器初始化完成: {new_pm}")
        logger.info(f"旧项目管理器初始化完成: {old_pm}")
        
        # 测试加载现有项目
        project_path = "output/感人故事"
        if os.path.exists(project_path):
            logger.info(f"测试加载项目: {project_path}")
            
            # 使用新项目管理器加载项目
            project_config = new_pm.load_project(project_path)
            if project_config:
                logger.info(f"新项目管理器加载成功: {project_config.get('project_name')}")
                logger.info(f"新项目管理器当前项目: {new_pm.current_project is not None}")
                
                # 模拟主窗口的同步操作
                old_pm.current_project = project_config
                old_pm.current_project_name = project_config.get('project_name', '')
                logger.info("已同步项目状态到旧项目管理器")
                
                # 验证同步结果
                logger.info(f"旧项目管理器当前项目: {old_pm.current_project is not None}")
                if old_pm.current_project:
                    logger.info(f"旧项目管理器项目名称: {old_pm.current_project.get('project_name')}")
                    
                # 测试角色场景管理器初始化
                if hasattr(old_pm, 'get_character_scene_manager'):
                    character_scene_manager = old_pm.get_character_scene_manager()
                    if character_scene_manager:
                        logger.info("角色场景管理器初始化成功")
                        return True
                    else:
                        logger.error("角色场景管理器初始化失败")
                        return False
                else:
                    logger.warning("旧项目管理器没有get_character_scene_manager方法")
                    return False
            else:
                logger.error("新项目管理器加载项目失败")
                return False
        else:
            logger.warning(f"测试项目不存在: {project_path}")
            return False
            
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

def test_create_new_project():
    """测试创建新项目"""
    try:
        logger.info("开始测试创建新项目...")
        
        # 初始化两个项目管理器
        new_pm = ProjectManager()
        old_pm = StoryboardProjectManager("config")
        
        # 创建测试项目
        test_project_name = "测试项目_修复验证"
        project_config = new_pm.create_new_project(test_project_name, "测试项目管理器修复")
        
        if project_config:
            logger.info(f"新项目创建成功: {project_config.get('project_name')}")
            
            # 模拟主窗口的同步操作
            old_pm.current_project = project_config
            old_pm.current_project_name = test_project_name
            logger.info("已同步新项目状态到旧项目管理器")
            
            # 验证同步结果
            logger.info(f"旧项目管理器当前项目: {old_pm.current_project is not None}")
            if old_pm.current_project:
                logger.info(f"旧项目管理器项目名称: {old_pm.current_project.get('project_name')}")
                
            # 清理测试项目
            project_dir = project_config.get('project_dir')
            if project_dir and os.path.exists(project_dir):
                import shutil
                shutil.rmtree(project_dir)
                logger.info(f"已清理测试项目: {project_dir}")
                
            return True
        else:
            logger.error("创建新项目失败")
            return False
            
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    logger.info("=" * 50)
    logger.info("开始项目管理器修复测试")
    logger.info("=" * 50)
    
    # 测试1: 加载现有项目
    test1_result = test_project_manager_sync()
    logger.info(f"测试1 - 加载现有项目: {'✅ 通过' if test1_result else '❌ 失败'}")
    
    # 测试2: 创建新项目
    test2_result = test_create_new_project()
    logger.info(f"测试2 - 创建新项目: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    # 总结
    logger.info("=" * 50)
    if test1_result and test2_result:
        logger.info("🎉 所有测试通过！项目管理器修复成功！")
    else:
        logger.error("❌ 部分测试失败，需要进一步修复")
    logger.info("=" * 50)
